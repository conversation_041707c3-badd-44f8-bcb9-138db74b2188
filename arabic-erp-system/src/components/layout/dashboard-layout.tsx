"use client"

import * as React from "react"
import Link from "next/link"
import { usePathname } from "next/navigation"
import {
  LayoutDashboard,
  Users,
  Package,
  ShoppingCart,
  BarChart3,
  Settings,
  Menu,
  X,
  LogOut,
  User,
} from "lucide-react"

import { cn } from "../../lib/utils"
import { Button } from "../ui/button"
import { Card } from "../ui/card"

interface DashboardLayoutProps {
  children: React.ReactNode
  userRole?: 'admin' | 'manager' | 'employee'
}

const navigationItems = {
  admin: [
    { name: "لوحة التحكم", href: "/dashboard", icon: LayoutDashboard },
    { name: "المستخدمين", href: "/users", icon: Users },
    { name: "المنتجات", href: "/products", icon: Package },
    { name: "الطلبات", href: "/orders", icon: ShoppingCart },
    { name: "التقارير", href: "/reports", icon: BarChart3 },
    { name: "الإعدادات", href: "/settings", icon: Settings },
  ],
  manager: [
    { name: "لوحة التحكم", href: "/dashboard", icon: LayoutDashboard },
    { name: "المنتجات", href: "/products", icon: Package },
    { name: "الطلبات", href: "/orders", icon: ShoppingCart },
    { name: "التقارير", href: "/reports", icon: BarChart3 },
  ],
  employee: [
    { name: "لوحة التحكم", href: "/dashboard", icon: LayoutDashboard },
    { name: "الطلبات", href: "/orders", icon: ShoppingCart },
  ],
}

export function DashboardLayout({ children, userRole = 'admin' }: DashboardLayoutProps) {
  const [sidebarOpen, setSidebarOpen] = React.useState(false)
  const pathname = usePathname()

  const navigation = navigationItems[userRole]

  return (
    <div className="min-h-screen bg-background" dir="rtl">
      {/* Mobile sidebar overlay */}
      {sidebarOpen && (
        <div
          className="fixed inset-0 z-40 bg-black bg-opacity-50 lg:hidden"
          onClick={() => setSidebarOpen(false)}
        />
      )}

      {/* Sidebar */}
      <div
        className={cn(
          "fixed inset-y-0 right-0 z-50 w-64 bg-card border-l transform transition-transform duration-300 ease-in-out lg:translate-x-0 lg:static lg:inset-0",
          sidebarOpen ? "translate-x-0" : "translate-x-full"
        )}
      >
        <div className="flex flex-col h-full">
          {/* Logo */}
          <div className="flex items-center justify-between h-16 px-6 border-b">
            <h1 className="text-xl font-bold text-primary">نظام الإدارة</h1>
            <Button
              variant="ghost"
              size="icon"
              className="lg:hidden"
              onClick={() => setSidebarOpen(false)}
            >
              <X className="h-6 w-6" />
            </Button>
          </div>

          {/* Navigation */}
          <nav className="flex-1 px-4 py-6 space-y-2">
            {navigation.map((item) => {
              const Icon = item.icon
              const isActive = pathname === item.href

              return (
                <Link
                  key={item.name}
                  href={item.href}
                  className={cn(
                    "flex items-center px-4 py-3 text-sm font-medium rounded-lg transition-colors",
                    isActive
                      ? "bg-primary text-primary-foreground"
                      : "text-muted-foreground hover:bg-accent hover:text-accent-foreground"
                  )}
                  onClick={() => setSidebarOpen(false)}
                >
                  <Icon className="ml-3 h-5 w-5" />
                  {item.name}
                </Link>
              )
            })}
          </nav>

          {/* User menu */}
          <div className="p-4 border-t">
            <div className="flex items-center space-x-3 mb-4">
              <div className="w-8 h-8 bg-primary rounded-full flex items-center justify-center">
                <User className="h-4 w-4 text-primary-foreground" />
              </div>
              <div className="flex-1 min-w-0">
                <p className="text-sm font-medium truncate">المستخدم الحالي</p>
                <p className="text-xs text-muted-foreground truncate">{userRole}</p>
              </div>
            </div>
            <Button variant="outline" size="sm" className="w-full">
              <LogOut className="ml-2 h-4 w-4" />
              تسجيل الخروج
            </Button>
          </div>
        </div>
      </div>

      {/* Main content */}
      <div className="lg:mr-64">
        {/* Top bar */}
        <header className="bg-card border-b h-16 flex items-center justify-between px-6">
          <Button
            variant="ghost"
            size="icon"
            className="lg:hidden"
            onClick={() => setSidebarOpen(true)}
          >
            <Menu className="h-6 w-6" />
          </Button>

          <div className="flex items-center space-x-4">
            <span className="text-sm text-muted-foreground">
              {new Date().toLocaleDateString('ar-SA', {
                weekday: 'long',
                year: 'numeric',
                month: 'long',
                day: 'numeric',
              })}
            </span>
          </div>
        </header>

        {/* Page content */}
        <main className="p-6">
          {children}
        </main>
      </div>
    </div>
  )
}
