"use client"

import * as React from "react"
import { Plus, Edit, Trash2, <PERSON> } from "lucide-react"

import { DashboardLayout } from "../src/components/layout/dashboard-layout"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "../src/components/ui/card"
import { DataTable } from "../src/components/ui/data-table"
import { Button } from "../src/components/ui/button"
import { formatCurrency, formatNumber } from "../src/lib/utils"

// Sample products data
const products = [
  {
    id: "PRD-001",
    name: "لابتوب ديل XPS 13",
    category: "أجهزة كمبيوتر",
    price: 4500,
    stock: 25,
    supplier: "شركة التقنية المتقدمة",
    sku: "DELL-XPS13-2024",
    status: "متوفر",
    description: "لابتوب عالي الأداء مع معالج Intel Core i7",
    weight: "1.2 كجم",
    dimensions: "30 × 20 × 1.5 سم",
    warranty: "سنتان",
    createdAt: "2024-01-10",
  },
  {
    id: "PRD-002",
    name: "هاتف آيفون 15 برو",
    category: "هواتف ذكية",
    price: 4200,
    stock: 15,
    supplier: "موزع آبل الرسمي",
    sku: "APPLE-IP15P-256",
    status: "متوفر",
    description: "هاتف ذكي بكاميرا احترافية ومعالج A17 Pro",
    weight: "187 جرام",
    dimensions: "14.7 × 7.1 × 0.8 سم",
    warranty: "سنة واحدة",
    createdAt: "2024-01-08",
  },
  {
    id: "PRD-003",
    name: "طابعة HP LaserJet Pro",
    category: "طابعات",
    price: 1200,
    stock: 8,
    supplier: "شركة المكتب الحديث",
    sku: "HP-LJ-P1102W",
    status: "قليل",
    description: "طابعة ليزر أحادية اللون مع واي فاي",
    weight: "5.2 كجم",
    dimensions: "35 × 24 × 18 سم",
    warranty: "سنة واحدة",
    createdAt: "2024-01-05",
  },
  {
    id: "PRD-004",
    name: "شاشة سامسونج 27 بوصة",
    category: "شاشات",
    price: 800,
    stock: 0,
    supplier: "موزع سامسونج",
    sku: "SAM-M27-4K",
    status: "نفد",
    description: "شاشة 4K مع تقنية HDR",
    weight: "4.1 كجم",
    dimensions: "61 × 36 × 8 سم",
    warranty: "ثلاث سنوات",
    createdAt: "2024-01-03",
  },
]

const columns = [
  {
    accessorKey: "id",
    header: "رقم المنتج",
  },
  {
    accessorKey: "name",
    header: "اسم المنتج",
    cell: ({ row }: any) => (
      <div className="font-medium">{row.getValue("name")}</div>
    ),
  },
  {
    accessorKey: "category",
    header: "الفئة",
  },
  {
    accessorKey: "price",
    header: "السعر",
    cell: ({ row }: any) => formatCurrency(row.getValue("price")),
  },
  {
    accessorKey: "stock",
    header: "المخزون",
    cell: ({ row }: any) => {
      const stock = row.getValue("stock") as number
      return (
        <span
          className={`px-2 py-1 rounded-full text-xs font-medium ${
            stock === 0
              ? "bg-red-100 text-red-800"
              : stock < 10
              ? "bg-yellow-100 text-yellow-800"
              : "bg-green-100 text-green-800"
          }`}
        >
          {formatNumber(stock)}
        </span>
      )
    },
  },
  {
    accessorKey: "supplier",
    header: "المورد",
  },
  {
    accessorKey: "status",
    header: "الحالة",
    cell: ({ row }: any) => {
      const status = row.getValue("status")
      return (
        <span
          className={`px-2 py-1 rounded-full text-xs font-medium ${
            status === "نفد"
              ? "bg-red-100 text-red-800"
              : status === "قليل"
              ? "bg-yellow-100 text-yellow-800"
              : "bg-green-100 text-green-800"
          }`}
        >
          {status}
        </span>
      )
    },
  },
  {
    id: "actions",
    header: "الإجراءات",
    cell: ({ row }: any) => (
      <div className="flex items-center space-x-2">
        <Button variant="ghost" size="icon">
          <Eye className="h-4 w-4" />
        </Button>
        <Button variant="ghost" size="icon">
          <Edit className="h-4 w-4" />
        </Button>
        <Button variant="ghost" size="icon">
          <Trash2 className="h-4 w-4" />
        </Button>
      </div>
    ),
  },
]

export default function ProductsPage() {
  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Page Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold">إدارة المنتجات</h1>
            <p className="text-muted-foreground">
              إدارة وتتبع جميع المنتجات في المخزون
            </p>
          </div>
          <Button>
            <Plus className="ml-2 h-4 w-4" />
            إضافة منتج جديد
          </Button>
        </div>

        {/* Stats Cards */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">إجمالي المنتجات</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{formatNumber(products.length)}</div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">المنتجات المتوفرة</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {formatNumber(products.filter(p => p.status === "متوفر").length)}
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">منتجات قليلة</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {formatNumber(products.filter(p => p.status === "قليل").length)}
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">منتجات نفدت</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {formatNumber(products.filter(p => p.status === "نفد").length)}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Products Table */}
        <Card>
          <CardHeader>
            <CardTitle>قائمة المنتجات</CardTitle>
            <CardDescription>
              جميع المنتجات المسجلة في النظام مع تفاصيلها الكاملة
            </CardDescription>
          </CardHeader>
          <CardContent>
            <DataTable
              columns={columns}
              data={products}
              searchKey="name"
            />
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  )
}
